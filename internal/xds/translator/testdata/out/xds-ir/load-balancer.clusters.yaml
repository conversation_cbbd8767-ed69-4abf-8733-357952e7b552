- circuitBreakers:
    thresholds:
    - maxRetries: 1024
  commonLbConfig: {}
  connectTimeout: 10s
  dnsLookupFamily: V4_PREFERRED
  edsClusterConfig:
    edsConfig:
      ads: {}
      resourceApiVersion: V3
    serviceName: first-route-dest
  ignoreHealthOnHostRemoval: true
  loadBalancingPolicy:
    policies:
    - typedExtensionConfig:
        name: envoy.load_balancing_policies.round_robin
        typedConfig:
          '@type': type.googleapis.com/envoy.extensions.load_balancing_policies.round_robin.v3.RoundRobin
          localityLbConfig:
            localityWeightedLbConfig: {}
  name: first-route-dest
  perConnectionBufferLimitBytes: 32768
  type: EDS
- circuitBreakers:
    thresholds:
    - maxRetries: 1024
  commonLbConfig: {}
  connectTimeout: 10s
  dnsLookupFamily: V4_PREFERRED
  edsClusterConfig:
    edsConfig:
      ads: {}
      resourceApiVersion: V3
    serviceName: second-route-dest
  ignoreHealthOnHostRemoval: true
  lbPolicy: RANDOM
  loadBalancingPolicy:
    policies:
    - typedExtensionConfig:
        name: envoy.load_balancing_policies.random
        typedConfig:
          '@type': type.googleapis.com/envoy.extensions.load_balancing_policies.random.v3.Random
          localityLbConfig:
            localityWeightedLbConfig: {}
  name: second-route-dest
  perConnectionBufferLimitBytes: 32768
  type: EDS
- circuitBreakers:
    thresholds:
    - maxRetries: 1024
  commonLbConfig: {}
  connectTimeout: 10s
  dnsLookupFamily: V4_PREFERRED
  edsClusterConfig:
    edsConfig:
      ads: {}
      resourceApiVersion: V3
    serviceName: third-route-dest
  ignoreHealthOnHostRemoval: true
  lbPolicy: LEAST_REQUEST
  loadBalancingPolicy:
    policies:
    - typedExtensionConfig:
        name: envoy.load_balancing_policies.least_request
        typedConfig:
          '@type': type.googleapis.com/envoy.extensions.load_balancing_policies.least_request.v3.LeastRequest
          localityLbConfig:
            localityWeightedLbConfig: {}
  name: third-route-dest
  perConnectionBufferLimitBytes: 32768
  type: EDS
- circuitBreakers:
    thresholds:
    - maxRetries: 1024
  commonLbConfig: {}
  connectTimeout: 10s
  dnsLookupFamily: V4_PREFERRED
  edsClusterConfig:
    edsConfig:
      ads: {}
      resourceApiVersion: V3
    serviceName: fourth-route-dest
  ignoreHealthOnHostRemoval: true
  lbPolicy: MAGLEV
  loadBalancingPolicy:
    policies:
    - typedExtensionConfig:
        name: envoy.load_balancing_policies.maglev
        typedConfig:
          '@type': type.googleapis.com/envoy.extensions.load_balancing_policies.maglev.v3.Maglev
  name: fourth-route-dest
  perConnectionBufferLimitBytes: 32768
  type: EDS
- circuitBreakers:
    thresholds:
    - maxRetries: 1024
  commonLbConfig: {}
  connectTimeout: 10s
  dnsLookupFamily: V4_PREFERRED
  edsClusterConfig:
    edsConfig:
      ads: {}
      resourceApiVersion: V3
    serviceName: fifth-route-dest
  ignoreHealthOnHostRemoval: true
  lbPolicy: LEAST_REQUEST
  loadBalancingPolicy:
    policies:
    - typedExtensionConfig:
        name: envoy.load_balancing_policies.least_request
        typedConfig:
          '@type': type.googleapis.com/envoy.extensions.load_balancing_policies.least_request.v3.LeastRequest
          localityLbConfig:
            localityWeightedLbConfig: {}
          slowStartConfig:
            slowStartWindow: 60s
  name: fifth-route-dest
  perConnectionBufferLimitBytes: 32768
  type: EDS
- circuitBreakers:
    thresholds:
    - maxRetries: 1024
  commonLbConfig: {}
  connectTimeout: 10s
  dnsLookupFamily: V4_PREFERRED
  edsClusterConfig:
    edsConfig:
      ads: {}
      resourceApiVersion: V3
    serviceName: sixth-route-dest
  ignoreHealthOnHostRemoval: true
  loadBalancingPolicy:
    policies:
    - typedExtensionConfig:
        name: envoy.load_balancing_policies.round_robin
        typedConfig:
          '@type': type.googleapis.com/envoy.extensions.load_balancing_policies.round_robin.v3.RoundRobin
          localityLbConfig:
            localityWeightedLbConfig: {}
          slowStartConfig:
            slowStartWindow: 300s
  name: sixth-route-dest
  perConnectionBufferLimitBytes: 32768
  type: EDS
- circuitBreakers:
    thresholds:
    - maxRetries: 1024
  commonLbConfig: {}
  connectTimeout: 10s
  dnsLookupFamily: V4_PREFERRED
  edsClusterConfig:
    edsConfig:
      ads: {}
      resourceApiVersion: V3
    serviceName: seventh-route-dest
  ignoreHealthOnHostRemoval: true
  lbPolicy: MAGLEV
  loadBalancingPolicy:
    policies:
    - typedExtensionConfig:
        name: envoy.load_balancing_policies.maglev
        typedConfig:
          '@type': type.googleapis.com/envoy.extensions.load_balancing_policies.maglev.v3.Maglev
  name: seventh-route-dest
  perConnectionBufferLimitBytes: 32768
  type: EDS
- circuitBreakers:
    thresholds:
    - maxRetries: 1024
  commonLbConfig: {}
  connectTimeout: 10s
  dnsLookupFamily: V4_PREFERRED
  edsClusterConfig:
    edsConfig:
      ads: {}
      resourceApiVersion: V3
    serviceName: eighth-route-dest
  ignoreHealthOnHostRemoval: true
  lbPolicy: MAGLEV
  loadBalancingPolicy:
    policies:
    - typedExtensionConfig:
        name: envoy.load_balancing_policies.maglev
        typedConfig:
          '@type': type.googleapis.com/envoy.extensions.load_balancing_policies.maglev.v3.Maglev
          tableSize: "524287"
  name: eighth-route-dest
  perConnectionBufferLimitBytes: 32768
  type: EDS
- circuitBreakers:
    thresholds:
    - maxRetries: 1024
  commonLbConfig: {}
  connectTimeout: 10s
  dnsLookupFamily: V4_PREFERRED
  edsClusterConfig:
    edsConfig:
      ads: {}
      resourceApiVersion: V3
    serviceName: ninth-route-dest
  ignoreHealthOnHostRemoval: true
  lbPolicy: MAGLEV
  loadBalancingPolicy:
    policies:
    - typedExtensionConfig:
        name: envoy.load_balancing_policies.maglev
        typedConfig:
          '@type': type.googleapis.com/envoy.extensions.load_balancing_policies.maglev.v3.Maglev
  name: ninth-route-dest
  perConnectionBufferLimitBytes: 32768
  type: EDS
- circuitBreakers:
    thresholds:
    - maxRetries: 1024
  commonLbConfig: {}
  connectTimeout: 10s
  dnsLookupFamily: V4_PREFERRED
  edsClusterConfig:
    edsConfig:
      ads: {}
      resourceApiVersion: V3
    serviceName: tenth-route-dest
  ignoreHealthOnHostRemoval: true
  lbPolicy: MAGLEV
  loadBalancingPolicy:
    policies:
    - typedExtensionConfig:
        name: envoy.load_balancing_policies.maglev
        typedConfig:
          '@type': type.googleapis.com/envoy.extensions.load_balancing_policies.maglev.v3.Maglev
  name: tenth-route-dest
  perConnectionBufferLimitBytes: 32768
  type: EDS
- circuitBreakers:
    thresholds:
    - maxRetries: 1024
  commonLbConfig: {}
  connectTimeout: 10s
  dnsLookupFamily: V4_PREFERRED
  edsClusterConfig:
    edsConfig:
      ads: {}
      resourceApiVersion: V3
    serviceName: eleventh-route-dest
  ignoreHealthOnHostRemoval: true
  lbPolicy: CLUSTER_PROVIDED
  loadBalancingPolicy:
    policies:
    - typedExtensionConfig:
        name: envoy.load_balancing_policies.override_host
        typedConfig:
          '@type': type.googleapis.com/envoy.extensions.load_balancing_policies.override_host.v3.OverrideHost
          fallbackPolicy:
            policies:
            - typedExtensionConfig:
                name: envoy.load_balancing_policies.round_robin
                typedConfig:
                  '@type': type.googleapis.com/envoy.extensions.load_balancing_policies.round_robin.v3.RoundRobin
          overrideHostSources:
          - header: x-custom-host
  name: eleventh-route-dest
  perConnectionBufferLimitBytes: 32768
  type: EDS
- circuitBreakers:
    thresholds:
    - maxRetries: 1024
  commonLbConfig: {}
  connectTimeout: 10s
  dnsLookupFamily: V4_PREFERRED
  edsClusterConfig:
    edsConfig:
      ads: {}
      resourceApiVersion: V3
    serviceName: twelfth-route-dest
  ignoreHealthOnHostRemoval: true
  lbPolicy: CLUSTER_PROVIDED
  loadBalancingPolicy:
    policies:
    - typedExtensionConfig:
        name: envoy.load_balancing_policies.override_host
        typedConfig:
          '@type': type.googleapis.com/envoy.extensions.load_balancing_policies.override_host.v3.OverrideHost
          fallbackPolicy:
            policies:
            - typedExtensionConfig:
                name: envoy.load_balancing_policies.least_request
                typedConfig:
                  '@type': type.googleapis.com/envoy.extensions.load_balancing_policies.least_request.v3.LeastRequest
          overrideHostSources:
          - metadata:
              key: custom.host
              path:
              - key: service
              - key: host
          - header: x-fallback-host
  name: twelfth-route-dest
  perConnectionBufferLimitBytes: 32768
  type: EDS
- circuitBreakers:
    thresholds:
    - maxRetries: 1024
  commonLbConfig: {}
  connectTimeout: 10s
  dnsLookupFamily: V4_PREFERRED
  edsClusterConfig:
    edsConfig:
      ads: {}
      resourceApiVersion: V3
    serviceName: thirteen-route-dest
  ignoreHealthOnHostRemoval: true
  lbPolicy: CLUSTER_PROVIDED
  loadBalancingPolicy:
    policies:
    - typedExtensionConfig:
        name: envoy.load_balancing_policies.override_host
        typedConfig:
          '@type': type.googleapis.com/envoy.extensions.load_balancing_policies.override_host.v3.OverrideHost
          fallbackPolicy:
            policies:
            - typedExtensionConfig:
                name: envoy.load_balancing_policies.least_request
                typedConfig:
                  '@type': type.googleapis.com/envoy.extensions.load_balancing_policies.least_request.v3.LeastRequest
          overrideHostSources:
          - metadata:
              key: custom.host
              path:
              - key: service
              - key: host
  name: thirteen-route-dest
  perConnectionBufferLimitBytes: 32768
  type: EDS
