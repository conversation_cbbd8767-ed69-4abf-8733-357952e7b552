gateways:
- apiVersion: gateway.networking.k8s.io/v1beta1
  kind: Gateway
  metadata:
    creationTimestamp: null
    name: gateway-1
    namespace: envoy-gateway
  spec:
    gatewayClassName: envoy-gateway-class
    listeners:
    - allowedRoutes:
        namespaces:
          from: All
      hostname: '*.envoyproxy.io'
      name: http
      port: 80
      protocol: HTTP
  status:
    listeners:
    - attachedRoutes: 1
      conditions:
      - lastTransitionTime: null
        message: Sending translated listener configuration to the data plane
        reason: Programmed
        status: "True"
        type: Programmed
      - lastTransitionTime: null
        message: Listener has been successfully translated
        reason: Accepted
        status: "True"
        type: Accepted
      - lastTransitionTime: null
        message: Listener references have been resolved
        reason: ResolvedRefs
        status: "True"
        type: ResolvedRefs
      name: http
      supportedKinds:
      - group: gateway.networking.k8s.io
        kind: HTTPRoute
      - group: gateway.networking.k8s.io
        kind: GRPCRoute
- apiVersion: gateway.networking.k8s.io/v1beta1
  kind: Gateway
  metadata:
    creationTimestamp: null
    name: gateway-2
    namespace: envoy-gateway
  spec:
    gatewayClassName: envoy-gateway-class
    listeners:
    - name: http-2
      port: 8888
      protocol: HTTP
    - allowedRoutes:
        namespaces:
          from: All
      hostname: example.com
      name: http-3
      port: 8888
      protocol: HTTP
  status:
    listeners:
    - attachedRoutes: 0
      conditions:
      - lastTransitionTime: null
        message: Sending translated listener configuration to the data plane
        reason: Programmed
        status: "True"
        type: Programmed
      - lastTransitionTime: null
        message: Listener has been successfully translated
        reason: Accepted
        status: "True"
        type: Accepted
      - lastTransitionTime: null
        message: Listener references have been resolved
        reason: ResolvedRefs
        status: "True"
        type: ResolvedRefs
      name: http-2
      supportedKinds:
      - group: gateway.networking.k8s.io
        kind: HTTPRoute
      - group: gateway.networking.k8s.io
        kind: GRPCRoute
    - attachedRoutes: 1
      conditions:
      - lastTransitionTime: null
        message: Sending translated listener configuration to the data plane
        reason: Programmed
        status: "True"
        type: Programmed
      - lastTransitionTime: null
        message: Listener has been successfully translated
        reason: Accepted
        status: "True"
        type: Accepted
      - lastTransitionTime: null
        message: Listener references have been resolved
        reason: ResolvedRefs
        status: "True"
        type: ResolvedRefs
      name: http-3
      supportedKinds:
      - group: gateway.networking.k8s.io
        kind: HTTPRoute
      - group: gateway.networking.k8s.io
        kind: GRPCRoute
httpRoutes:
- apiVersion: gateway.networking.k8s.io/v1beta1
  kind: HTTPRoute
  metadata:
    creationTimestamp: null
    name: httproute-1
    namespace: default
  spec:
    hostnames:
    - gateway.envoyproxy.io
    parentRefs:
    - name: gateway-1
      namespace: envoy-gateway
      sectionName: http
    rules:
    - backendRefs:
      - name: service-1
        port: 8080
      matches:
      - path:
          value: /
  status:
    parents:
    - conditions:
      - lastTransitionTime: null
        message: Route is accepted
        reason: Accepted
        status: "True"
        type: Accepted
      - lastTransitionTime: null
        message: Resolved all the Object references for the Route
        reason: ResolvedRefs
        status: "True"
        type: ResolvedRefs
      controllerName: gateway.envoyproxy.io/gatewayclass-controller
      parentRef:
        name: gateway-1
        namespace: envoy-gateway
        sectionName: http
- apiVersion: gateway.networking.k8s.io/v1beta1
  kind: HTTPRoute
  metadata:
    creationTimestamp: null
    name: httproute-2
    namespace: default
  spec:
    hostnames:
    - example.com
    parentRefs:
    - name: gateway-2
      namespace: envoy-gateway
      sectionName: http-3
    rules:
    - backendRefs:
      - name: service-2
        port: 8080
      matches:
      - path:
          value: /
  status:
    parents:
    - conditions:
      - lastTransitionTime: null
        message: Route is accepted
        reason: Accepted
        status: "True"
        type: Accepted
      - lastTransitionTime: null
        message: Resolved all the Object references for the Route
        reason: ResolvedRefs
        status: "True"
        type: ResolvedRefs
      controllerName: gateway.envoyproxy.io/gatewayclass-controller
      parentRef:
        name: gateway-2
        namespace: envoy-gateway
        sectionName: http-3
infraIR:
  envoy-gateway-class:
    proxy:
      config:
        apiVersion: gateway.envoyproxy.io/v1alpha1
        kind: EnvoyProxy
        metadata:
          creationTimestamp: null
          name: test
          namespace: envoy-gateway
        spec:
          logging: {}
          mergeGateways: true
          telemetry:
            tracing:
              provider:
                host: otel-collector.monitoring.svc.cluster.local
                port: 4317
                type: OpenTelemetry
              samplingRate: 100
        status: {}
      listeners:
      - address: null
        name: envoy-gateway/gateway-1/http
        ports:
        - containerPort: 10080
          name: http-80
          protocol: HTTP
          servicePort: 80
      - address: null
        name: envoy-gateway/gateway-2/http-2
        ports:
        - containerPort: 8888
          name: http-8888
          protocol: HTTP
          servicePort: 8888
      metadata:
        labels:
          gateway.envoyproxy.io/owning-gatewayclass: envoy-gateway-class
        ownerReference:
          kind: GatewayClass
          name: envoy-gateway-class
      name: envoy-gateway-class
      namespace: envoy-gateway-system
xdsIR:
  envoy-gateway-class:
    accessLog:
      json:
      - path: /dev/stdout
    http:
    - address: 0.0.0.0
      hostnames:
      - '*.envoyproxy.io'
      isHTTP2: false
      metadata:
        kind: Gateway
        name: gateway-1
        namespace: envoy-gateway
        sectionName: http
      name: envoy-gateway/gateway-1/http
      path:
        escapedSlashesAction: UnescapeAndRedirect
        mergeSlashes: true
      port: 10080
      routes:
      - destination:
          metadata:
            kind: HTTPRoute
            name: httproute-1
            namespace: default
          name: httproute/default/httproute-1/rule/0
          settings:
          - addressType: IP
            endpoints:
            - host: *******
              port: 8080
            metadata:
              name: service-1
              namespace: default
              sectionName: "8080"
            name: httproute/default/httproute-1/rule/0/backend/0
            protocol: HTTP
            weight: 1
        hostname: gateway.envoyproxy.io
        isHTTP2: false
        metadata:
          kind: HTTPRoute
          name: httproute-1
          namespace: default
        name: httproute/default/httproute-1/rule/0/match/0/gateway_envoyproxy_io
        pathMatch:
          distinct: false
          name: ""
          prefix: /
    - address: 0.0.0.0
      hostnames:
      - '*'
      isHTTP2: false
      metadata:
        kind: Gateway
        name: gateway-2
        namespace: envoy-gateway
        sectionName: http-2
      name: envoy-gateway/gateway-2/http-2
      path:
        escapedSlashesAction: UnescapeAndRedirect
        mergeSlashes: true
      port: 8888
    - address: 0.0.0.0
      hostnames:
      - example.com
      isHTTP2: false
      metadata:
        kind: Gateway
        name: gateway-2
        namespace: envoy-gateway
        sectionName: http-3
      name: envoy-gateway/gateway-2/http-3
      path:
        escapedSlashesAction: UnescapeAndRedirect
        mergeSlashes: true
      port: 8888
      routes:
      - destination:
          metadata:
            kind: HTTPRoute
            name: httproute-2
            namespace: default
          name: httproute/default/httproute-2/rule/0
          settings:
          - addressType: IP
            endpoints:
            - host: *******
              port: 8080
            metadata:
              name: service-2
              namespace: default
              sectionName: "8080"
            name: httproute/default/httproute-2/rule/0/backend/0
            protocol: HTTP
            weight: 1
        hostname: example.com
        isHTTP2: false
        metadata:
          kind: HTTPRoute
          name: httproute-2
          namespace: default
        name: httproute/default/httproute-2/rule/0/match/0/example_com
        pathMatch:
          distinct: false
          name: ""
          prefix: /
    readyListener:
      address: 0.0.0.0
      ipFamily: IPv4
      path: /ready
      port: 19003
    tracing:
      authority: otel-collector.monitoring.svc.cluster.local
      destination:
        metadata:
          kind: EnvoyProxy
          name: test
          namespace: envoy-gateway
        name: tracing
        settings:
        - endpoints:
          - host: otel-collector.monitoring.svc.cluster.local
            port: 4317
          name: tracing/backend/-1
          protocol: GRPC
          weight: 1
      provider:
        host: otel-collector.monitoring.svc.cluster.local
        port: 4317
        type: OpenTelemetry
      samplingRate: 100
      serviceName: envoy-gateway-class
