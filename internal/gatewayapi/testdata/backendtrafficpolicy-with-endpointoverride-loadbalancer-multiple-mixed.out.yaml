backendTrafficPolicies:
- apiVersion: gateway.envoyproxy.io/v1alpha1
  kind: BackendTrafficPolicy
  metadata:
    creationTimestamp: null
    name: policy-for-header-override
    namespace: default
  spec:
    loadBalancer:
      endpointOverride:
        extractFrom:
        - metadata:
            key: custom.host
            path:
            - key: service
            - key: host
        - header: x-fallback-host
      type: RoundRobin
    targetRef:
      group: gateway.networking.k8s.io
      kind: HTTPRoute
      name: httproute-1
  status:
    ancestors:
    - ancestorRef:
        group: gateway.networking.k8s.io
        kind: Gateway
        name: gateway-1
        namespace: envoy-gateway
        sectionName: http
      conditions:
      - lastTransitionTime: null
        message: Policy has been accepted.
        reason: Accepted
        status: "True"
        type: Accepted
      controllerName: gateway.envoyproxy.io/gatewayclass-controller
- apiVersion: gateway.envoyproxy.io/v1alpha1
  kind: BackendTrafficPolicy
  metadata:
    creationTimestamp: null
    name: policy-for-metadata-override
    namespace: default
  spec:
    loadBalancer:
      endpointOverride:
        extractFrom:
        - metadata:
            key: custom.host
            path:
            - key: service
            - key: host
        - header: x-fallback-host
      type: LeastRequest
    targetRef:
      group: gateway.networking.k8s.io
      kind: HTTPRoute
      name: httproute-2
  status:
    ancestors:
    - ancestorRef:
        group: gateway.networking.k8s.io
        kind: Gateway
        name: gateway-1
        namespace: envoy-gateway
        sectionName: http
      conditions:
      - lastTransitionTime: null
        message: Policy has been accepted.
        reason: Accepted
        status: "True"
        type: Accepted
      controllerName: gateway.envoyproxy.io/gatewayclass-controller
gateways:
- apiVersion: gateway.networking.k8s.io/v1
  kind: Gateway
  metadata:
    creationTimestamp: null
    name: gateway-1
    namespace: envoy-gateway
  spec:
    gatewayClassName: envoy-gateway-class
    listeners:
    - allowedRoutes:
        namespaces:
          from: All
      name: http
      port: 80
      protocol: HTTP
  status:
    listeners:
    - attachedRoutes: 2
      conditions:
      - lastTransitionTime: null
        message: Sending translated listener configuration to the data plane
        reason: Programmed
        status: "True"
        type: Programmed
      - lastTransitionTime: null
        message: Listener has been successfully translated
        reason: Accepted
        status: "True"
        type: Accepted
      - lastTransitionTime: null
        message: Listener references have been resolved
        reason: ResolvedRefs
        status: "True"
        type: ResolvedRefs
      name: http
      supportedKinds:
      - group: gateway.networking.k8s.io
        kind: HTTPRoute
      - group: gateway.networking.k8s.io
        kind: GRPCRoute
httpRoutes:
- apiVersion: gateway.networking.k8s.io/v1
  kind: HTTPRoute
  metadata:
    creationTimestamp: null
    name: httproute-1
    namespace: default
  spec:
    hostnames:
    - gateway.envoyproxy.io
    parentRefs:
    - name: gateway-1
      namespace: envoy-gateway
      sectionName: http
    rules:
    - backendRefs:
      - name: service-1
        port: 8080
      matches:
      - path:
          value: /header-override
  status:
    parents:
    - conditions:
      - lastTransitionTime: null
        message: Route is accepted
        reason: Accepted
        status: "True"
        type: Accepted
      - lastTransitionTime: null
        message: Resolved all the Object references for the Route
        reason: ResolvedRefs
        status: "True"
        type: ResolvedRefs
      controllerName: gateway.envoyproxy.io/gatewayclass-controller
      parentRef:
        name: gateway-1
        namespace: envoy-gateway
        sectionName: http
- apiVersion: gateway.networking.k8s.io/v1
  kind: HTTPRoute
  metadata:
    creationTimestamp: null
    name: httproute-2
    namespace: default
  spec:
    hostnames:
    - gateway.envoyproxy.io
    parentRefs:
    - name: gateway-1
      namespace: envoy-gateway
      sectionName: http
    rules:
    - backendRefs:
      - name: service-2
        port: 8080
      matches:
      - path:
          value: /metadata-override
  status:
    parents:
    - conditions:
      - lastTransitionTime: null
        message: Route is accepted
        reason: Accepted
        status: "True"
        type: Accepted
      - lastTransitionTime: null
        message: Resolved all the Object references for the Route
        reason: ResolvedRefs
        status: "True"
        type: ResolvedRefs
      controllerName: gateway.envoyproxy.io/gatewayclass-controller
      parentRef:
        name: gateway-1
        namespace: envoy-gateway
        sectionName: http
infraIR:
  envoy-gateway/gateway-1:
    proxy:
      listeners:
      - address: null
        name: envoy-gateway/gateway-1/http
        ports:
        - containerPort: 10080
          name: http-80
          protocol: HTTP
          servicePort: 80
      metadata:
        labels:
          gateway.envoyproxy.io/owning-gateway-name: gateway-1
          gateway.envoyproxy.io/owning-gateway-namespace: envoy-gateway
        ownerReference:
          kind: GatewayClass
          name: envoy-gateway-class
      name: envoy-gateway/gateway-1
      namespace: envoy-gateway-system
xdsIR:
  envoy-gateway/gateway-1:
    accessLog:
      json:
      - path: /dev/stdout
    http:
    - address: 0.0.0.0
      hostnames:
      - '*'
      isHTTP2: false
      metadata:
        kind: Gateway
        name: gateway-1
        namespace: envoy-gateway
        sectionName: http
      name: envoy-gateway/gateway-1/http
      path:
        escapedSlashesAction: UnescapeAndRedirect
        mergeSlashes: true
      port: 10080
      routes:
      - destination:
          metadata:
            kind: HTTPRoute
            name: httproute-2
            namespace: default
          name: httproute/default/httproute-2/rule/0
          settings:
          - addressType: IP
            endpoints:
            - host: *******
              port: 8080
            - host: *******
              port: 8080
            metadata:
              name: service-2
              namespace: default
              sectionName: "8080"
            name: httproute/default/httproute-2/rule/0/backend/0
            protocol: HTTP
            weight: 1
        hostname: gateway.envoyproxy.io
        isHTTP2: false
        metadata:
          kind: HTTPRoute
          name: httproute-2
          namespace: default
        name: httproute/default/httproute-2/rule/0/match/0/gateway_envoyproxy_io
        pathMatch:
          distinct: false
          name: ""
          prefix: /metadata-override
        traffic:
          loadBalancer:
            endpointOverride:
              extractFrom:
              - metadata:
                  key: custom.host
                  path:
                  - key: service
                  - key: host
              - header: x-fallback-host
            leastRequest: {}
      - destination:
          metadata:
            kind: HTTPRoute
            name: httproute-1
            namespace: default
          name: httproute/default/httproute-1/rule/0
          settings:
          - addressType: IP
            endpoints:
            - host: *******
              port: 8080
            - host: *******
              port: 8080
            metadata:
              name: service-1
              namespace: default
              sectionName: "8080"
            name: httproute/default/httproute-1/rule/0/backend/0
            protocol: HTTP
            weight: 1
        hostname: gateway.envoyproxy.io
        isHTTP2: false
        metadata:
          kind: HTTPRoute
          name: httproute-1
          namespace: default
        name: httproute/default/httproute-1/rule/0/match/0/gateway_envoyproxy_io
        pathMatch:
          distinct: false
          name: ""
          prefix: /header-override
        traffic:
          loadBalancer:
            endpointOverride:
              extractFrom:
              - metadata:
                  key: custom.host
                  path:
                  - key: service
                  - key: host
              - header: x-fallback-host
            roundRobin: {}
    readyListener:
      address: 0.0.0.0
      ipFamily: IPv4
      path: /ready
      port: 19003
