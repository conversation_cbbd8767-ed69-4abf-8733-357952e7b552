apiVersion: v1
kind: Service
metadata:
  name: lb-backend-endpointoverride
  namespace: gateway-conformance-infra
spec:
  selector:
    app: lb-backend-endpointoverride
  ports:
    - protocol: TCP
      port: 8080
      targetPort: 3000
---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: lb-backend-endpointoverride
  namespace: gateway-conformance-infra
  labels:
    app: lb-backend-endpointoverride
spec:
  replicas: 3
  selector:
    matchLabels:
      app: lb-backend-endpointoverride
  template:
    metadata:
      labels:
        app: lb-backend-endpointoverride
    spec:
      containers:
        - name: backend
          image: gcr.io/k8s-staging-gateway-api/echo-basic:v20231214-v1.0.0-140-gf544a46e
          imagePullPolicy: IfNotPresent
          env:
            - name: POD_NAME
              valueFrom:
                fieldRef:
                  fieldPath: metadata.name
            - name: POD_IP
              valueFrom:
                fieldRef:
                  fieldPath: status.podIP
            - name: NAMESPACE
              valueFrom:
                fieldRef:
                  fieldPath: metadata.namespace
            - name: SERVICE_NAME
              value: lb-backend-endpointoverride
          resources:
            requests:
              cpu: 10m
---
apiVersion: gateway.envoyproxy.io/v1alpha1
kind: BackendTrafficPolicy
metadata:
  name: endpoint-override-header-lb-policy
  namespace: gateway-conformance-infra
spec:
  targetRefs:
    - group: gateway.networking.k8s.io
      kind: HTTPRoute
      name: endpoint-override-header-lb-route
  loadBalancer:
    type: RoundRobin
    endpointOverride:
      extractFrom:
        - header: "x-custom-host"

---
apiVersion: gateway.networking.k8s.io/v1
kind: HTTPRoute
metadata:
  name: endpoint-override-header-lb-route
  namespace: gateway-conformance-infra
spec:
  parentRefs:
    - name: same-namespace
  rules:
    - matches:
        - path:
            type: PathPrefix
            value: /endpoint-override-header
      backendRefs:
        - name: lb-backend-endpointoverride
          port: 8080

